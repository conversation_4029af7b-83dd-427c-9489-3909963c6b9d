import { NextResponse } from "next/server";

function rollDice() {
  return [Math.floor(Math.random() * 6) + 1, Math.floor(Math.random() * 6) + 1];
}

const POP_COMBOS = [
  [5, 2],
  [2, 5],
  [4, 3],
  [3, 4],
  [6, 1],
  [1, 6],
  [6, 5],
  [5, 6],
];
const KRAP_COMBOS = [
  [2, 1],
  [1, 2],
  [1, 1],
  [6, 6],
];

export async function GET() {
  const initialRoll = rollDice();
  let outcome = "";
  let reason = "";
  let rolls = [];

  // Check for Pop (win)
  if (POP_COMBOS.some(([a, b]) => a === initialRoll[0] && b === initialRoll[1])) {
    outcome = "win";
    reason = "pop";
    rolls = [];
  } else if (KRAP_COMBOS.some(([a, b]) => a === initialRoll[0] && b === initialRoll[1])) {
    outcome = "lose";
    reason = "krap";
    rolls = [];
  } else {
    // Keep rolling until total is 5 (win) or 7 (lose)
    let done = false;
    while (!done) {
      const roll = rollDice();
      rolls.push(roll);
      const total = roll[0] + roll[1];
      if (total === 5) {
        outcome = "win";
        reason = "rolled 5";
        done = true;
      } else if (total === 7) {
        outcome = "lose";
        reason = "rolled 7";
        done = true;
      }
    }
  }

  return NextResponse.json({
    initialRoll,
    rolls,
    outcome,
    reason,
  });
}