"use client";

import { useState, useEffect } from "react";
import { useAccount } from "wagmi";
import { Button } from "./components/DemoComponents";
import { TransactionButton } from "@coinbase/onchainkit/transaction";

function Dice({ value, rolling }: { value: number; rolling: boolean }) {
  return (
    <div
      className={`w-16 h-16 flex items-center justify-center rounded-lg border-4 border-black bg-white text-4xl font-bold shadow-lg transition-transform duration-300 ${
        rolling ? "animate-bounce" : ""
      }`}
    >
      {rolling ? "?" : value}
    </div>
  );
}

const TOKENS = [
  { symbol: "ETH", decimals: 18 },
  { symbol: "USDC", decimals: 6 },
];

export default function DiceGame() {
  const { address, isConnected } = useAccount();
  const [isRolling, setIsRolling] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [showConfetti, setShow<PERSON>onfetti] = useState(false);
  const [mode, setMode] = useState<"offchain" | "onchain">("offchain");
  const [betToken, setBetToken] = useState("ETH");
  const [betAmount, setBetAmount] = useState(0.01);
  const [txPending, setTxPending] = useState(false);
  const [txError, setTxError] = useState("");

  // Placeholder for balance check (implement with OnchainKit as needed)
  const [balance, setBalance] = useState<number | null>(null);
  useEffect(() => {
    // TODO: Fetch balance for selected token using OnchainKit
    setBalance(100); // For demo, assume enough balance
  }, [address, betToken]);

  const handleRoll = async () => {
    setIsRolling(true);
    setResult(null);
    setShowConfetti(false);
    setTxError("");
    try {
      const res = await fetch("/api/roll");
      const data = await res.json();
      console.log("Roll result:", data); // Debug log
      setResult(data);
      if (data.outcome === "win") {
        setShowConfetti(true);
        setTimeout(() => setShowConfetti(false), 2000);
      }
    } catch (err) {
      console.error("Roll error:", err); // Debug log
      setResult({ error: "Failed to roll dice." });
    }
    setIsRolling(false);
  };

  const handleOnchainBet = async () => {
    setTxPending(true);
    setTxError("");
    // 1. Trigger escrow transaction (replace with actual contract call)
    // 2. On success, call /api/roll
    // 3. If win, trigger payout transaction
    // 4. Show feedback
    try {
      // TODO: Replace with TransactionButton or custom contract call
      await new Promise((resolve) => setTimeout(resolve, 2000)); // Simulate tx

      // Call handleRoll and wait for the result
      setIsRolling(true);
      setResult(null);
      setShowConfetti(false);

      const res = await fetch("/api/roll");
      const data = await res.json();
      setResult(data);

      if (data.outcome === "win") {
        setShowConfetti(true);
        setTimeout(() => setShowConfetti(false), 2000);
        // TODO: Trigger payout transaction
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }

      setIsRolling(false);
    } catch (e) {
      setTxError("Onchain transaction failed");
      setIsRolling(false);
    }
    setTxPending(false);
  };

  const canBet = isConnected && betAmount > 0 && (balance === null || betAmount <= balance);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-green-900">
      <h1 className="text-3xl font-extrabold text-white mb-6 drop-shadow-lg">Pop Ma Dice 🎲</h1>
      <div className="mb-4 flex gap-4">
        <Button
          variant={mode === "offchain" ? "primary" : "outline"}
          onClick={() => setMode("offchain")}
        >
          Offchain
        </Button>
        <Button
          variant={mode === "onchain" ? "primary" : "outline"}
          onClick={() => setMode("onchain")}
        >
          Onchain
        </Button>
      </div>
      <div className="flex space-x-8 mb-8">
        <Dice
          value={result?.initialRoll?.[0] ?? 1}
          rolling={isRolling || txPending}
        />
        <Dice
          value={result?.initialRoll?.[1] ?? 1}
          rolling={isRolling || txPending}
        />
      </div>
      {mode === "onchain" && (
        <div className="mb-6 flex flex-col items-center gap-2 w-full max-w-xs">
          <div className="flex gap-2 w-full">
            <select
              className="flex-1 px-2 py-2 rounded border"
              value={betToken}
              onChange={e => setBetToken(e.target.value)}
            >
              {TOKENS.map(t => (
                <option key={t.symbol} value={t.symbol}>{t.symbol}</option>
              ))}
            </select>
            <input
              type="number"
              min="0.0001"
              step="0.0001"
              className="flex-1 px-2 py-2 rounded border"
              value={betAmount}
              onChange={e => setBetAmount(Number(e.target.value))}
              disabled={!isConnected}
            />
          </div>
          <div className="text-xs text-white mt-1">
            Balance: {balance !== null ? `${balance} ${betToken}` : "-"}
          </div>
        </div>
      )}
      <div className="mb-4">
        {mode === "onchain" ? (
          <Button
            onClick={handleOnchainBet}
            disabled={!canBet || isRolling || txPending}
            variant="primary"
            size="lg"
          >
            {txPending ? "Placing Bet..." : `Place Bet + Roll (${betAmount} ${betToken})`}
          </Button>
        ) : (
          <Button
            onClick={handleRoll}
            disabled={isRolling}
            variant="primary"
            size="lg"
          >
            {isRolling ? "Rolling..." : "Roll Dice"}
          </Button>
        )}
      </div>
      <div className="mb-4 text-white text-sm">
        {isConnected ? (
          <span>Wallet: <span className="font-mono">{address}</span></span>
        ) : (
          <span className="text-red-300">Connect your wallet to play onchain</span>
        )}
      </div>
      <div className="mt-8 w-full max-w-md">
        {result && (
          <div className="bg-white rounded-lg p-6 shadow-xl">
            <div className="mb-2 text-lg font-semibold">Initial Roll: <span className="font-mono">[{result.initialRoll?.join(", ")}]</span></div>
            {result.rolls && result.rolls.length > 0 && (
              <div className="mb-2 text-sm">Additional Rolls:
                <ul className="list-disc ml-6">
                  {result.rolls.map((roll: number[], idx: number) => (
                    <li key={idx} className="font-mono">[{roll.join(", ")}]</li>
                  ))}
                </ul>
              </div>
            )}
            <div className={`mt-4 text-2xl font-bold flex items-center ${result.outcome === "win" ? "text-green-600" : "text-red-600 animate-shake"}`}>
              {result.outcome === "win" ? "POP!" : result.outcome === "lose" ? "KRAP!" : ""}
              <span className="ml-3 text-base font-normal">({result.reason})</span>
            </div>
            {showConfetti && (
              <div className="absolute left-0 right-0 top-0 h-32 flex justify-center items-center pointer-events-none">
                <span className="text-5xl animate-bounce">🎉🎉🎉</span>
              </div>
            )}
            {result.error && (
              <div className="mt-2 text-red-700">{result.error}</div>
            )}
            {txError && (
              <div className="mt-2 text-red-700">{txError}</div>
            )}
          </div>
        )}
      </div>
      <style jsx global>{`
        body { background: #166534; }
        .glow { box-shadow: 0 0 20px 5px #ffd700; }
        @keyframes shake {
          0% { transform: translateX(0); }
          25% { transform: translateX(-5px); }
          50% { transform: translateX(5px); }
          75% { transform: translateX(-5px); }
          100% { transform: translateX(0); }
        }
        .animate-shake { animation: shake 0.5s; }
      `}</style>
    </div>
  );
}
