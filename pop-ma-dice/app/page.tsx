"use client";

import { useState } from "react";
import { useAccount, useBalance } from "wagmi";
import { Button } from "./components/DemoComponents";
import {
  Transaction,
  TransactionButton,
  TransactionStatus,
  TransactionStatusLabel,
  TransactionStatusAction
} from "@coinbase/onchainkit/transaction";
import { formatEther, parseEther } from "viem";
import { base } from "wagmi/chains";

function DiceFace({ value }: { value: number }) {
  // Create authentic dice face patterns using dots
  const dot = "w-3.5 h-3.5 bg-gray-800 rounded-full shadow-inner";
  const empty = "w-3.5 h-3.5"; // Empty space

  switch (value) {
    case 1:
      return (
        <div className="grid grid-cols-3 gap-1 p-2">
          <div className={empty}></div>
          <div className={empty}></div>
          <div className={empty}></div>
          <div className={empty}></div>
          <div className={dot}></div>
          <div className={empty}></div>
          <div className={empty}></div>
          <div className={empty}></div>
          <div className={empty}></div>
        </div>
      );
    case 2:
      return (
        <div className="grid grid-cols-3 gap-1 p-2">
          <div className={dot}></div>
          <div className={empty}></div>
          <div className={empty}></div>
          <div className={empty}></div>
          <div className={empty}></div>
          <div className={empty}></div>
          <div className={empty}></div>
          <div className={empty}></div>
          <div className={dot}></div>
        </div>
      );
    case 3:
      return (
        <div className="grid grid-cols-3 gap-1 p-2">
          <div className={dot}></div>
          <div className={empty}></div>
          <div className={empty}></div>
          <div className={empty}></div>
          <div className={dot}></div>
          <div className={empty}></div>
          <div className={empty}></div>
          <div className={empty}></div>
          <div className={dot}></div>
        </div>
      );
    case 4:
      return (
        <div className="grid grid-cols-3 gap-1 p-2">
          <div className={dot}></div>
          <div className={empty}></div>
          <div className={dot}></div>
          <div className={empty}></div>
          <div className={empty}></div>
          <div className={empty}></div>
          <div className={dot}></div>
          <div className={empty}></div>
          <div className={dot}></div>
        </div>
      );
    case 5:
      return (
        <div className="grid grid-cols-3 gap-1 p-2">
          <div className={dot}></div>
          <div className={empty}></div>
          <div className={dot}></div>
          <div className={empty}></div>
          <div className={dot}></div>
          <div className={empty}></div>
          <div className={dot}></div>
          <div className={empty}></div>
          <div className={dot}></div>
        </div>
      );
    case 6:
      return (
        <div className="grid grid-cols-3 gap-1 p-2">
          <div className={dot}></div>
          <div className={empty}></div>
          <div className={dot}></div>
          <div className={dot}></div>
          <div className={empty}></div>
          <div className={dot}></div>
          <div className={dot}></div>
          <div className={empty}></div>
          <div className={dot}></div>
        </div>
      );
    default:
      return <div className="text-2xl font-bold text-black">?</div>;
  }
}

function Dice({ value, rolling }: { value: number; rolling: boolean }) {
  // Ensure value is a valid dice number (1-6)
  const diceValue = rolling ? 0 : (value >= 1 && value <= 6 ? value : 1);

  return (
    <div
      className={`w-24 h-24 flex items-center justify-center rounded-xl border-4 border-gray-800 bg-gradient-to-br from-white to-gray-100 shadow-xl transition-all duration-300 ${
        rolling ? "animate-bounce scale-110" : "hover:scale-105"
      }`}
      style={{
        boxShadow: rolling
          ? "0 10px 25px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.6)"
          : "0 8px 20px rgba(0,0,0,0.2), inset 0 1px 0 rgba(255,255,255,0.6)"
      }}
    >
      {rolling ? (
        <div className="text-4xl font-bold text-gray-600 animate-pulse">?</div>
      ) : (
        <DiceFace value={diceValue} />
      )}
    </div>
  );
}

const TOKENS = [
  {
    symbol: "ETH",
    decimals: 18,
    address: undefined // Native ETH
  },
  {
    symbol: "USDC",
    decimals: 6,
    address: "******************************************" as `0x${string}` // USDC on Base
  },
];

export default function DiceGame() {
  const { address, isConnected } = useAccount();
  const [isRolling, setIsRolling] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [showConfetti, setShowConfetti] = useState(false);
  const [mode, setMode] = useState<"offchain" | "onchain">("offchain");
  const [betToken, setBetToken] = useState("ETH");
  const [betAmount, setBetAmount] = useState(0.01);

  const [txError, setTxError] = useState("");
  const [gameState, setGameState] = useState<"idle" | "continue" | "finished">("idle");
  const [currentDice, setCurrentDice] = useState<[number, number]>([1, 1]);
  const [additionalRolls, setAdditionalRolls] = useState<Array<{roll: [number, number], total: number}>>([]);

  // Get the current token configuration
  const currentToken = TOKENS.find(t => t.symbol === betToken);

  // Fetch balance using wagmi's useBalance hook
  const { data: balanceData, isLoading: balanceLoading, error: balanceError } = useBalance({
    address: address,
    token: currentToken?.address, // undefined for ETH (native token)
  });

  // Debug logging
  console.log("Balance Debug:", {
    betToken,
    currentToken,
    address,
    balanceData,
    balanceLoading,
    balanceError
  });

  // Convert balance to number for display
  const balance = balanceData ?
    currentToken?.decimals === 6 ?
      parseFloat((Number(balanceData.value) / 1e6).toString()) : // USDC has 6 decimals
      parseFloat(formatEther(balanceData.value)) : // ETH has 18 decimals
    null;

  const handleRoll = async () => {
    setIsRolling(true);
    setResult(null);
    setShowConfetti(false);
    setTxError("");
    setAdditionalRolls([]);
    try {
      const res = await fetch("/api/roll");
      const data = await res.json();
      console.log("Initial roll result:", data); // Debug log
      setResult(data);
      setCurrentDice(data.initialRoll);
      setGameState(data.gameState);

      if (data.outcome === "win") {
        setShowConfetti(true);
        setTimeout(() => setShowConfetti(false), 2000);
      }
    } catch (err) {
      console.error("Roll error:", err); // Debug log
      setResult({ error: "Failed to roll dice." });
      setGameState("idle");
    }
    setIsRolling(false);
  };

  const handleContinueRoll = async () => {
    setIsRolling(true);
    try {
      const res = await fetch("/api/roll/continue");
      const data = await res.json();
      console.log("Continue roll result:", data); // Debug log

      // Update current dice to show the new roll
      setCurrentDice(data.roll);

      // Add this roll to the additional rolls history
      setAdditionalRolls(prev => [...prev, { roll: data.roll, total: data.total }]);

      // Update game state and result
      setGameState(data.gameState);
      setResult((prev: any) => ({
        ...prev,
        outcome: data.outcome,
        reason: data.reason,
        lastRoll: data.roll,
        lastTotal: data.total
      }));

      if (data.outcome === "win") {
        setShowConfetti(true);
        setTimeout(() => setShowConfetti(false), 2000);
      }
    } catch (err) {
      console.error("Continue roll error:", err);
      setResult((prev: any) => ({ ...prev, error: "Failed to continue rolling." }));
    }
    setIsRolling(false);
  };

  // Create transaction calls for the dice game
  const createBetTransaction = () => {
    // For demo purposes, we'll create a simple ETH transfer to simulate a bet
    // In a real implementation, this would call a smart contract like:
    // - DiceGame.placeBet(betAmount) for placing bets
    // - DiceGame.claimWinnings() for claiming winnings
    const calls = [{
      to: '******************************************' as `0x${string}`, // Null address for demo
      data: '0x' as `0x${string}`,
      value: parseEther(betAmount.toString()),
    }];

    return calls;
  };

  const handleOnchainSuccess = async () => {
    // Transaction succeeded, now roll the dice
    await handleRoll();
  };

  const handleOnchainError = (error: any) => {
    setTxError(`Transaction failed: ${error?.message || 'Unknown error'}`);
    setGameState("idle");
  };

  const canBet = isConnected && betAmount > 0 && balance !== null && betAmount <= balance;

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-green-900">
      <h1 className="text-3xl font-extrabold text-white mb-6 drop-shadow-lg">Pop Ma Dice 🎲</h1>
      <div className="mb-4 flex gap-4">
        <Button
          variant={mode === "offchain" ? "primary" : "outline"}
          onClick={() => setMode("offchain")}
        >
          Offchain
        </Button>
        <Button
          variant={mode === "onchain" ? "primary" : "outline"}
          onClick={() => setMode("onchain")}
        >
          Onchain
        </Button>
      </div>
      <div className="flex space-x-12 mb-8">
        <Dice
          value={currentDice[0]}
          rolling={isRolling}
        />
        <Dice
          value={currentDice[1]}
          rolling={isRolling}
        />
      </div>

      {mode === "onchain" && (
        <div className="mb-6 flex flex-col items-center gap-2 w-full max-w-xs">
          <div className="flex gap-2 w-full">
            <select
              className="flex-1 px-2 py-2 rounded border"
              value={betToken}
              onChange={e => setBetToken(e.target.value)}
            >
              {TOKENS.map(t => (
                <option key={t.symbol} value={t.symbol}>{t.symbol}</option>
              ))}
            </select>
            <input
              type="number"
              min="0.0001"
              step="0.0001"
              className="flex-1 px-2 py-2 rounded border"
              value={betAmount}
              onChange={e => setBetAmount(Number(e.target.value))}
              disabled={!isConnected}
            />
          </div>
          <div className="text-xs text-white mt-1">
            Balance: {
              !isConnected ? "Connect wallet" :
              balanceLoading ? "Loading..." :
              balanceError ? "Error loading balance" :
              balance !== null ? `${balance.toFixed(4)} ${betToken}` :
              "0.0000 " + betToken
            }
          </div>
        </div>
      )}
      <div className="mb-4 flex gap-4">
        {gameState === "idle" ? (
          // Initial roll buttons
          mode === "onchain" ? (
            <Transaction
              calls={createBetTransaction()}
              onSuccess={handleOnchainSuccess}
              onError={handleOnchainError}
            >
              <TransactionButton
                disabled={!canBet || isRolling}
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg text-lg"
                text={`Place Bet + Roll (${betAmount} ${betToken})`}
              />
              <TransactionStatus>
                <TransactionStatusLabel />
                <TransactionStatusAction />
              </TransactionStatus>
            </Transaction>
          ) : (
            <Button
              onClick={handleRoll}
              disabled={isRolling}
              variant="primary"
              size="lg"
            >
              {isRolling ? "Rolling..." : "Roll Dice"}
            </Button>
          )
        ) : gameState === "continue" ? (
          // Continue rolling button
          <Button
            onClick={handleContinueRoll}
            disabled={isRolling}
            variant="primary"
            size="lg"
          >
            {isRolling ? "Rolling..." : "Roll Again"}
          </Button>
        ) : (
          // Game finished - new game button
          <Button
            onClick={() => {
              setGameState("idle");
              setResult(null);
              setCurrentDice([1, 1]);
              setAdditionalRolls([]);
            }}
            variant="outline"
            size="lg"
          >
            New Game
          </Button>
        )}
      </div>
      <div className="mb-4 text-white text-sm">
        {isConnected ? (
          <span>Wallet: <span className="font-mono">{address}</span></span>
        ) : (
          <span className="text-red-300">Connect your wallet to play onchain</span>
        )}
      </div>
      <div className="mt-8 w-full max-w-md">
        {result && (
          <div className="bg-white rounded-lg p-6 shadow-xl">
            <div className="mb-2 text-lg font-semibold">Initial Roll: <span className="font-mono">[{result.initialRoll?.join(", ")}]</span></div>
            {additionalRolls.length > 0 && (
              <div className="mb-2 text-sm">Additional Rolls:
                <ul className="list-disc ml-6">
                  {additionalRolls.map((rollData, idx) => (
                    <li key={idx} className="font-mono">[{rollData.roll.join(", ")}] = {rollData.total}</li>
                  ))}
                </ul>
              </div>
            )}
            <div className={`mt-4 text-2xl font-bold flex items-center ${result.outcome === "win" ? "text-green-600" : result.outcome === "lose" ? "text-red-600 animate-shake" : "text-blue-600"}`}>
              {result.outcome === "win" ? "POP!" : result.outcome === "lose" ? "KRAP!" : ""}
              <span className="ml-3 text-base font-normal">({result.reason})</span>
            </div>
            {showConfetti && (
              <div className="absolute left-0 right-0 top-0 h-32 flex justify-center items-center pointer-events-none">
                <span className="text-5xl animate-bounce">🎉🎉🎉</span>
              </div>
            )}
            {result.error && (
              <div className="mt-2 text-red-700">{result.error}</div>
            )}
            {txError && (
              <div className="mt-2 text-red-700">{txError}</div>
            )}
          </div>
        )}
      </div>
      <style jsx global>{`
        body { background: #166534; }
        .glow { box-shadow: 0 0 20px 5px #ffd700; }
        @keyframes shake {
          0% { transform: translateX(0); }
          25% { transform: translateX(-5px); }
          50% { transform: translateX(5px); }
          75% { transform: translateX(-5px); }
          100% { transform: translateX(0); }
        }
        .animate-shake { animation: shake 0.5s; }
      `}</style>
    </div>
  );
}
